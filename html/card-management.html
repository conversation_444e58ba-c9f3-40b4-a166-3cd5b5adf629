<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片管理</title>
    <style>
        .page-content {
            padding: 32px;
            background: #161a1e;
            min-height: 100vh;
        }

        /* 快捷操作区域 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .action-card {
            background: #21262d;
            border: 1px solid #2a2e33;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            background: #2a2e33;
            border-color: #f7931a;
            transform: translateY(-2px);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: #f0f6fc;
            margin-bottom: 4px;
        }

        .action-desc {
            font-size: 14px;
            color: #8b949e;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .card-item {
            background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
            border-radius: 16px;
            padding: 28px;
            color: white;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(247, 147, 26, 0.3);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-label {
            font-size: 16px;
            font-weight: 600;
        }

        .card-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }

        .status-frozen {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .status-cancelled {
            background: rgba(149, 165, 166, 0.2);
            color: #95a5a6;
        }

        .card-number {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 2px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
        }

        .card-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .card-info-item {
            text-align: center;
        }

        .card-info-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .card-info-value {
            font-size: 14px;
            font-weight: 600;
        }

        .card-balance {
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 20px;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-danger {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .btn-danger:hover {
            background: rgba(231, 76, 60, 0.3);
        }

        .btn-success {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .btn-success:hover {
            background: rgba(46, 204, 113, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .empty-state p {
            font-size: 16px;
            margin-bottom: 30px;
        }

        .btn-large {
            padding: 12px 30px;
            font-size: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .btn-large:hover {
            transform: translateY(-2px);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .modal h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn-modal {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel {
            background: #95a5a6;
            color: white;
        }

        .btn-confirm {
            background: #667eea;
            color: white;
        }

        .btn-modal:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <a href="#" class="action-card" onclick="window.parent.loadPage('card-application')">
                <span class="action-icon">📝</span>
                <div class="action-title">申请新卡</div>
                <div class="action-desc">快速申请UCard虚拟信用卡</div>
            </a>

            <a href="#" class="action-card" onclick="window.parent.loadPage('card-recharge')">
                <span class="action-icon">💰</span>
                <div class="action-title">卡片充值</div>
                <div class="action-desc">为现有卡片充值数字货币</div>
            </a>

            <a href="#" class="action-card" onclick="window.parent.loadPage('transaction-query')">
                <span class="action-icon">📊</span>
                <div class="action-title">交易查询</div>
                <div class="action-desc">查看详细交易记录</div>
            </a>

            <a href="#" class="action-card" onclick="window.parent.loadPage('balance-query')">
                <span class="action-icon">💵</span>
                <div class="action-title">余额查询</div>
                <div class="action-desc">查看账户余额明细</div>
            </a>
        </div>

        <!-- 卡片列表 -->
        <div class="card-grid" id="cardGrid">
            <!-- 示例卡片 -->
            <div class="card-item">
                <div class="card-header">
                    <div class="card-label">购物卡</div>
                    <div class="card-status status-normal">正常</div>
                </div>
                <div class="card-number">**** **** **** 1234</div>
                <div class="card-info">
                    <div class="card-info-item">
                        <div class="card-info-label">有效期</div>
                        <div class="card-info-value">12/26</div>
                    </div>
                    <div class="card-info-item">
                        <div class="card-info-label">开卡时间</div>
                        <div class="card-info-value">2024-01-15</div>
                    </div>
                </div>
                <div class="card-balance">$1,250.00</div>
                <div class="card-actions">
                    <button class="btn btn-primary" onclick="viewCardDetails('1234')">详情</button>
                    <button class="btn btn-primary" onclick="setLimit('1234')">限额</button>
                    <button class="btn btn-danger" onclick="freezeCard('1234')">冻结</button>
                </div>
            </div>

            <div class="card-item">
                <div class="card-header">
                    <div class="card-label">订阅卡</div>
                    <div class="card-status status-frozen">已冻结</div>
                </div>
                <div class="card-number">**** **** **** 5678</div>
                <div class="card-info">
                    <div class="card-info-item">
                        <div class="card-info-label">有效期</div>
                        <div class="card-info-value">08/25</div>
                    </div>
                    <div class="card-info-item">
                        <div class="card-info-label">开卡时间</div>
                        <div class="card-info-value">2023-08-20</div>
                    </div>
                </div>
                <div class="card-balance">$500.00</div>
                <div class="card-actions">
                    <button class="btn btn-primary" onclick="viewCardDetails('5678')">详情</button>
                    <button class="btn btn-success" onclick="unfreezeCard('5678')">解冻</button>
                    <button class="btn btn-danger" onclick="cancelCard('5678')">注销</button>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="icon">💳</div>
            <h3>暂无卡片</h3>
            <p>您还没有申请任何UCard，立即申请开始使用吧！</p>
            <button class="btn-large" onclick="window.parent.loadPage('card-application')">立即申请</button>
        </div>

        <!-- 卡片详情模态框 -->
        <div id="cardDetailsModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('cardDetailsModal')">&times;</span>
                <h3>卡片详情</h3>
                <div class="form-group">
                    <label>卡片标签</label>
                    <input type="text" id="cardLabel" readonly>
                </div>
                <div class="form-group">
                    <label>完整卡号</label>
                    <input type="text" id="fullCardNumber" readonly>
                </div>
                <div class="form-group">
                    <label>有效期</label>
                    <input type="text" id="expiryDate" readonly>
                </div>
                <div class="form-group">
                    <label>CVV <small>(需要二次验证)</small></label>
                    <input type="password" id="cvv" readonly placeholder="点击查看CVV">
                </div>
                <div class="modal-actions">
                    <button class="btn-modal btn-primary" onclick="showCVV()">查看CVV</button>
                    <button class="btn-modal btn-cancel" onclick="closeModal('cardDetailsModal')">关闭</button>
                </div>
            </div>
        </div>

        <!-- 设置限额模态框 -->
        <div id="setLimitModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('setLimitModal')">&times;</span>
                <h3>设置消费限额</h3>
                <div class="form-group">
                    <label>每日限额 (USD)</label>
                    <input type="number" id="dailyLimit" placeholder="输入每日消费限额">
                </div>
                <div class="form-group">
                    <label>每月限额 (USD)</label>
                    <input type="number" id="monthlyLimit" placeholder="输入每月消费限额">
                </div>
                <div class="modal-actions">
                    <button class="btn-modal btn-cancel" onclick="closeModal('setLimitModal')">取消</button>
                    <button class="btn-modal btn-confirm" onclick="confirmSetLimit()">确认设置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 查看卡片详情
        function viewCardDetails(cardId) {
            // 模拟获取卡片详情
            const cardDetails = {
                '1234': {
                    label: '购物卡',
                    fullNumber: '4532 1234 5678 1234',
                    expiry: '12/26',
                    cvv: '123'
                },
                '5678': {
                    label: '订阅卡',
                    fullNumber: '4532 5678 9012 5678',
                    expiry: '08/25',
                    cvv: '456'
                }
            };

            const details = cardDetails[cardId];
            if (details) {
                document.getElementById('cardLabel').value = details.label;
                document.getElementById('fullCardNumber').value = details.fullNumber;
                document.getElementById('expiryDate').value = details.expiry;
                document.getElementById('cvv').value = '';
                document.getElementById('cvv').placeholder = '点击查看CVV';
                document.getElementById('cardDetailsModal').style.display = 'block';
            }
        }

        // 显示CVV
        function showCVV() {
            // 模拟二次验证
            if (confirm('确认查看CVV？这需要进行安全验证。')) {
                document.getElementById('cvv').type = 'text';
                document.getElementById('cvv').value = '123';
                setTimeout(() => {
                    document.getElementById('cvv').type = 'password';
                    document.getElementById('cvv').value = '';
                    document.getElementById('cvv').placeholder = '已隐藏CVV';
                }, 10000); // 10秒后自动隐藏
            }
        }

        // 设置限额
        function setLimit(cardId) {
            document.getElementById('setLimitModal').style.display = 'block';
        }

        function confirmSetLimit() {
            const dailyLimit = document.getElementById('dailyLimit').value;
            const monthlyLimit = document.getElementById('monthlyLimit').value;
            
            if (dailyLimit || monthlyLimit) {
                alert(`限额设置成功！\n每日限额: $${dailyLimit || '无限制'}\n每月限额: $${monthlyLimit || '无限制'}`);
                closeModal('setLimitModal');
            } else {
                alert('请至少设置一个限额');
            }
        }

        // 冻结卡片
        function freezeCard(cardId) {
            if (confirm('确认冻结此卡片？冻结后将无法进行消费。')) {
                alert('卡片已冻结');
                // 这里应该调用API更新卡片状态
            }
        }

        // 解冻卡片
        function unfreezeCard(cardId) {
            if (confirm('确认解冻此卡片？')) {
                alert('卡片已解冻');
                // 这里应该调用API更新卡片状态
            }
        }

        // 注销卡片
        function cancelCard(cardId) {
            if (confirm('确认注销此卡片？注销后将永久失效，余额将退回账户。')) {
                alert('卡片注销申请已提交');
                // 这里应该调用API处理注销
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
