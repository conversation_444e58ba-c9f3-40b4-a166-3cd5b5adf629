<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片充值</title>
    <style>
        .page-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .recharge-form {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .card-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }

        .card-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .card-option.selected {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-label {
            font-size: 16px;
            font-weight: 600;
        }

        .card-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }

        .card-number {
            font-size: 16px;
            font-weight: 500;
            letter-spacing: 1px;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .card-balance {
            font-size: 20px;
            font-weight: 700;
            text-align: right;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .form-group small {
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .quick-select {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-btn:hover,
        .quick-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .account-selector {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .account-selector:hover,
        .account-selector.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .account-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .account-type {
            font-weight: 500;
            color: #2c3e50;
        }

        .account-balance {
            font-size: 14px;
            color: #27ae60;
            font-weight: 600;
        }

        .exchange-rate {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
        }

        .rate-info {
            font-size: 14px;
            color: #1976d2;
            margin-bottom: 5px;
        }

        .rate-time {
            font-size: 12px;
            color: #757575;
        }

        .calculation-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .calc-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .calc-row.total {
            border-top: 1px solid #ddd;
            padding-top: 10px;
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
        }

        .calc-label {
            color: #6c757d;
        }

        .calc-value {
            font-weight: 500;
        }

        .submit-section {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
        }

        .btn-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            min-width: 200px;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
        }

        .btn-submit:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .empty-state p {
            font-size: 16px;
            margin-bottom: 30px;
        }

        .btn-large {
            padding: 12px 30px;
            font-size: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .btn-large:hover {
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .card-selector {
                grid-template-columns: 1fr;
            }
            
            .quick-select {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <form class="recharge-form" id="rechargeForm">
            <!-- 选择充值卡片 -->
            <div class="form-section">
                <h3 class="section-title">
                    <span class="icon">💳</span>
                    选择充值卡片
                </h3>
                
                <div class="card-selector" id="cardSelector">
                    <div class="card-option" onclick="selectCard('1234')">
                        <div class="card-header">
                            <div class="card-label">购物卡</div>
                            <div class="card-status">正常</div>
                        </div>
                        <div class="card-number">**** **** **** 1234</div>
                        <div class="card-balance">$1,250.00</div>
                    </div>

                    <div class="card-option" onclick="selectCard('5678')">
                        <div class="card-header">
                            <div class="card-label">订阅卡</div>
                            <div class="card-status">正常</div>
                        </div>
                        <div class="card-number">**** **** **** 5678</div>
                        <div class="card-balance">$500.00</div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="icon">💳</div>
                    <h3>暂无可充值卡片</h3>
                    <p>您还没有可用的卡片，请先申请开卡</p>
                    <button class="btn-large" onclick="window.parent.loadPage('card-application')">立即开卡</button>
                </div>
            </div>

            <!-- 充值信息 -->
            <div class="form-section" id="rechargeSection" style="display: none;">
                <h3 class="section-title">
                    <span class="icon">💰</span>
                    充值信息
                </h3>

                <div class="form-group">
                    <label for="rechargeAmount">充值金额 (USD)</label>
                    <input type="number" id="rechargeAmount" placeholder="输入充值金额" min="10" step="0.01" oninput="calculateTotal()">
                    <div class="quick-select">
                        <button type="button" class="quick-btn" onclick="selectAmount(100)">$100</button>
                        <button type="button" class="quick-btn" onclick="selectAmount(500)">$500</button>
                        <button type="button" class="quick-btn" onclick="selectAmount(1000)">$1000</button>
                    </div>
                    <small>最低充值金额 $10</small>
                </div>

                <div class="form-group">
                    <label>选择扣款账户</label>
                    <div class="account-selector" onclick="selectAccount('usdt')">
                        <div class="account-info">
                            <span class="account-type">USDT 账户</span>
                            <span class="account-balance">余额: 2,500.00 USDT</span>
                        </div>
                    </div>
                    <div class="account-selector" onclick="selectAccount('usdc')">
                        <div class="account-info">
                            <span class="account-type">USDC 账户</span>
                            <span class="account-balance">余额: 1,800.50 USDC</span>
                        </div>
                    </div>
                </div>

                <div class="exchange-rate" id="exchangeRate" style="display: none;">
                    <div class="rate-info">当前汇率: 1 USDT = 0.9998 USD</div>
                    <div class="rate-time">更新时间: 2024-01-20 14:30:00</div>
                </div>

                <div class="calculation-box" id="calculationBox">
                    <div class="calc-row">
                        <span class="calc-label">充值金额:</span>
                        <span class="calc-value" id="chargeAmount">$0.00</span>
                    </div>
                    <div class="calc-row">
                        <span class="calc-label">充值手续费 (2%):</span>
                        <span class="calc-value" id="chargeFee">$0.00</span>
                    </div>
                    <div class="calc-row total">
                        <span class="calc-label">总计扣款:</span>
                        <span class="calc-value" id="totalAmount">0.00 USDT</span>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-section" id="submitSection" style="display: none;">
                <button type="submit" class="btn-submit" id="submitBtn" disabled>
                    确认充值
                </button>
                <div style="margin-top: 15px; color: #7f8c8d; font-size: 14px;">
                    点击确认后将从选定账户扣除相应金额
                </div>
            </div>
        </form>
    </div>

    <script>
        let selectedCardId = '';
        let selectedAccount = '';

        // 选择卡片
        function selectCard(cardId) {
            selectedCardId = cardId;
            
            // 更新卡片选择状态
            document.querySelectorAll('.card-option').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.card-option').classList.add('selected');
            
            // 显示充值信息部分
            document.getElementById('rechargeSection').style.display = 'block';
            document.getElementById('submitSection').style.display = 'block';
            
            checkFormValidity();
        }

        // 选择充值金额
        function selectAmount(amount) {
            document.getElementById('rechargeAmount').value = amount;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            calculateTotal();
        }

        // 选择账户
        function selectAccount(type) {
            selectedAccount = type;
            document.querySelectorAll('.account-selector').forEach(selector => {
                selector.classList.remove('selected');
            });
            event.target.closest('.account-selector').classList.add('selected');
            
            // 显示汇率信息
            document.getElementById('exchangeRate').style.display = 'block';
            
            // 更新汇率显示
            const rateInfo = document.querySelector('.rate-info');
            if (type === 'usdt') {
                rateInfo.textContent = '当前汇率: 1 USDT = 0.9998 USD';
            } else {
                rateInfo.textContent = '当前汇率: 1 USDC = 1.0001 USD';
            }
            
            calculateTotal();
            checkFormValidity();
        }

        // 计算总金额
        function calculateTotal() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const chargeFee = rechargeAmount * 0.02; // 2% 手续费
            const totalUSD = rechargeAmount + chargeFee;
            
            // 模拟汇率转换
            let exchangeRate = 1;
            if (selectedAccount === 'usdt') {
                exchangeRate = 1.0002;
            } else if (selectedAccount === 'usdc') {
                exchangeRate = 0.9999;
            }
            
            const totalCrypto = totalUSD * exchangeRate;
            
            document.getElementById('chargeAmount').textContent = `$${rechargeAmount.toFixed(2)}`;
            document.getElementById('chargeFee').textContent = `$${chargeFee.toFixed(2)}`;
            
            if (selectedAccount) {
                document.getElementById('totalAmount').textContent = `${totalCrypto.toFixed(4)} ${selectedAccount.toUpperCase()}`;
            } else {
                document.getElementById('totalAmount').textContent = '请选择扣款账户';
            }
            
            checkFormValidity();
        }

        // 检查表单有效性
        function checkFormValidity() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const cardSelected = selectedCardId !== '';
            const amountValid = rechargeAmount >= 10;
            const accountValid = selectedAccount !== '';
            
            const isValid = cardSelected && amountValid && accountValid;
            document.getElementById('submitBtn').disabled = !isValid;
        }

        // 表单提交
        document.getElementById('rechargeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const rechargeAmount = document.getElementById('rechargeAmount').value;
            const totalAmount = document.getElementById('totalAmount').textContent;
            
            if (confirm(`确认充值 $${rechargeAmount} 到选定卡片？\n将扣除 ${totalAmount}`)) {
                // 模拟充值过程
                document.getElementById('submitBtn').textContent = '处理中...';
                document.getElementById('submitBtn').disabled = true;
                
                setTimeout(() => {
                    alert(`充值成功！\n\n充值金额: $${rechargeAmount}\n扣除金额: ${totalAmount}\n\n卡片余额已更新。`);
                    
                    // 重置表单
                    document.getElementById('rechargeForm').reset();
                    selectedCardId = '';
                    selectedAccount = '';
                    document.querySelectorAll('.selected').forEach(el => el.classList.remove('selected'));
                    document.getElementById('rechargeSection').style.display = 'none';
                    document.getElementById('submitSection').style.display = 'none';
                    document.getElementById('exchangeRate').style.display = 'none';
                    
                    // 可以选择跳转到卡片管理页面
                    if (confirm('是否查看卡片详情？')) {
                        if (window.parent && window.parent.loadPage) {
                            window.parent.loadPage('card-management');
                        }
                    }
                }, 2000);
            }
        });

        // 监听充值金额输入
        document.getElementById('rechargeAmount').addEventListener('input', calculateTotal);

        // 检查是否有可用卡片
        function checkAvailableCards() {
            const cardSelector = document.getElementById('cardSelector');
            const emptyState = document.getElementById('emptyState');
            
            // 这里可以根据实际情况判断是否有可用卡片
            const hasCards = cardSelector.children.length > 0;
            
            if (!hasCards) {
                cardSelector.style.display = 'none';
                emptyState.style.display = 'block';
            }
        }

        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', checkAvailableCards);
    </script>
</body>
</html>
