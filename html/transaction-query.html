<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易查询</title>
    <style>
        .page-content {
            padding: 32px;
            background: #161a1e;
            min-height: 100vh;
        }

        .filter-section {
            background: #21262d;
            border: 1px solid #2a2e33;
            padding: 32px;
            border-radius: 12px;
            margin-bottom: 24px;
        }

        .filter-title {
            font-size: 20px;
            font-weight: 600;
            color: #f0f6fc;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
        }

        .filter-title .icon {
            margin-right: 12px;
            font-size: 20px;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .transaction-list {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .list-header {
            background: #f8f9fa;
            padding: 20px 25px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .list-count {
            font-size: 14px;
            color: #7f8c8d;
        }

        .transaction-item {
            padding: 20px 25px;
            border-bottom: 1px solid #f1f3f4;
            transition: background-color 0.3s ease;
            cursor: pointer;
        }

        .transaction-item:hover {
            background-color: #f8f9fa;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .transaction-merchant {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .transaction-amount {
            font-size: 16px;
            font-weight: 600;
        }

        .amount-positive {
            color: #27ae60;
        }

        .amount-negative {
            color: #e74c3c;
        }

        .transaction-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #7f8c8d;
        }

        .transaction-time {
            margin-right: 15px;
        }

        .transaction-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-refunded {
            background: #cce5ff;
            color: #004085;
        }

        .transaction-type {
            font-size: 12px;
            color: #6c757d;
            margin-left: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .empty-state p {
            font-size: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            position: relative;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #2c3e50;
        }

        .detail-value {
            color: #7f8c8d;
            text-align: right;
        }

        @media (max-width: 768px) {
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .filter-actions {
                justify-content: stretch;
            }
            
            .filter-actions .btn {
                flex: 1;
            }
            
            .transaction-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .transaction-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <!-- 筛选条件 -->
        <div class="filter-section">
            <h3 class="filter-title">
                <span class="icon">🔍</span>
                筛选条件
            </h3>
            
            <div class="filter-row">
                <div class="filter-group">
                    <label for="cardSelect">选择卡片</label>
                    <select id="cardSelect">
                        <option value="">全部卡片</option>
                        <option value="1234">购物卡 (**** 1234)</option>
                        <option value="5678">订阅卡 (**** 5678)</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="statusSelect">交易状态</label>
                    <select id="statusSelect">
                        <option value="">全部状态</option>
                        <option value="success">成功</option>
                        <option value="pending">处理中</option>
                        <option value="failed">失败</option>
                        <option value="refunded">已退款</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="typeSelect">交易类型</label>
                    <select id="typeSelect">
                        <option value="">全部类型</option>
                        <option value="purchase">消费</option>
                        <option value="refund">退款</option>
                        <option value="recharge">充值</option>
                    </select>
                </div>
            </div>
            
            <div class="filter-row">
                <div class="filter-group">
                    <label for="startDate">开始日期</label>
                    <input type="date" id="startDate">
                </div>
                
                <div class="filter-group">
                    <label for="endDate">结束日期</label>
                    <input type="date" id="endDate">
                </div>
                
                <div class="filter-group">
                    <label for="merchantSearch">商户名称</label>
                    <input type="text" id="merchantSearch" placeholder="搜索商户名称">
                </div>
            </div>
            
            <div class="filter-actions">
                <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                <button class="btn btn-primary" onclick="applyFilters()">查询</button>
            </div>
        </div>

        <!-- 交易列表 -->
        <div class="transaction-list">
            <div class="list-header">
                <div class="list-title">交易记录</div>
                <div class="list-count">共 <span id="totalCount">25</span> 条记录</div>
            </div>
            
            <div id="transactionContainer">
                <!-- 示例交易记录 -->
                <div class="transaction-item" onclick="showTransactionDetail('tx001')">
                    <div class="transaction-header">
                        <div class="transaction-merchant">Amazon.com</div>
                        <div class="transaction-amount amount-negative">-$89.99</div>
                    </div>
                    <div class="transaction-details">
                        <div>
                            <span class="transaction-time">2024-01-20 14:30:25</span>
                            <span class="transaction-type">线上消费</span>
                        </div>
                        <div class="transaction-status status-success">交易成功</div>
                    </div>
                </div>

                <div class="transaction-item" onclick="showTransactionDetail('tx002')">
                    <div class="transaction-header">
                        <div class="transaction-merchant">Netflix</div>
                        <div class="transaction-amount amount-negative">-$15.99</div>
                    </div>
                    <div class="transaction-details">
                        <div>
                            <span class="transaction-time">2024-01-19 09:15:42</span>
                            <span class="transaction-type">订阅服务</span>
                        </div>
                        <div class="transaction-status status-success">交易成功</div>
                    </div>
                </div>

                <div class="transaction-item" onclick="showTransactionDetail('tx003')">
                    <div class="transaction-header">
                        <div class="transaction-merchant">USDT 充值</div>
                        <div class="transaction-amount amount-positive">+$500.00</div>
                    </div>
                    <div class="transaction-details">
                        <div>
                            <span class="transaction-time">2024-01-18 16:22:10</span>
                            <span class="transaction-type">账户充值</span>
                        </div>
                        <div class="transaction-status status-success">充值成功</div>
                    </div>
                </div>

                <div class="transaction-item" onclick="showTransactionDetail('tx004')">
                    <div class="transaction-header">
                        <div class="transaction-merchant">Steam Store</div>
                        <div class="transaction-amount amount-negative">-$59.99</div>
                    </div>
                    <div class="transaction-details">
                        <div>
                            <span class="transaction-time">2024-01-17 20:45:33</span>
                            <span class="transaction-type">游戏购买</span>
                        </div>
                        <div class="transaction-status status-failed">交易失败</div>
                    </div>
                </div>

                <div class="transaction-item" onclick="showTransactionDetail('tx005')">
                    <div class="transaction-header">
                        <div class="transaction-merchant">Spotify</div>
                        <div class="transaction-amount amount-positive">+$9.99</div>
                    </div>
                    <div class="transaction-details">
                        <div>
                            <span class="transaction-time">2024-01-16 11:30:15</span>
                            <span class="transaction-type">退款</span>
                        </div>
                        <div class="transaction-status status-refunded">退款成功</div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="icon">📊</div>
                <h3>暂无交易记录</h3>
                <p>没有找到符合条件的交易记录</p>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <button onclick="changePage(-1)" id="prevBtn">上一页</button>
                <button class="current">1</button>
                <button onclick="changePage(1)">2</button>
                <button onclick="changePage(1)">3</button>
                <button onclick="changePage(1)" id="nextBtn">下一页</button>
            </div>
        </div>

        <!-- 交易详情模态框 -->
        <div id="transactionDetailModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal()">&times;</span>
                <h3>交易详情</h3>
                <div id="transactionDetails">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 3;

        // 模拟交易详情数据
        const transactionDetails = {
            'tx001': {
                merchant: 'Amazon.com',
                amount: '-$89.99',
                originalAmount: '-$89.99 USD',
                time: '2024-01-20 14:30:25',
                status: '交易成功',
                type: '线上消费',
                cardNumber: '**** **** **** 1234',
                authCode: 'AUTH123456',
                merchantCategory: '电商购物',
                transactionId: 'TXN20240120143025001'
            },
            'tx002': {
                merchant: 'Netflix',
                amount: '-$15.99',
                originalAmount: '-$15.99 USD',
                time: '2024-01-19 09:15:42',
                status: '交易成功',
                type: '订阅服务',
                cardNumber: '**** **** **** 5678',
                authCode: 'AUTH789012',
                merchantCategory: '娱乐服务',
                transactionId: 'TXN20240119091542002'
            }
        };

        // 显示交易详情
        function showTransactionDetail(transactionId) {
            const detail = transactionDetails[transactionId];
            if (!detail) {
                alert('交易详情不存在');
                return;
            }

            const detailsHtml = `
                <div class="detail-row">
                    <span class="detail-label">商户名称:</span>
                    <span class="detail-value">${detail.merchant}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">交易金额:</span>
                    <span class="detail-value">${detail.amount}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">原始金额:</span>
                    <span class="detail-value">${detail.originalAmount}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">交易时间:</span>
                    <span class="detail-value">${detail.time}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">交易状态:</span>
                    <span class="detail-value">${detail.status}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">交易类型:</span>
                    <span class="detail-value">${detail.type}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">使用卡片:</span>
                    <span class="detail-value">${detail.cardNumber}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">授权码:</span>
                    <span class="detail-value">${detail.authCode}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">商户类别:</span>
                    <span class="detail-value">${detail.merchantCategory}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">交易ID:</span>
                    <span class="detail-value">${detail.transactionId}</span>
                </div>
            `;

            document.getElementById('transactionDetails').innerHTML = detailsHtml;
            document.getElementById('transactionDetailModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('transactionDetailModal').style.display = 'none';
        }

        // 应用筛选条件
        function applyFilters() {
            const cardSelect = document.getElementById('cardSelect').value;
            const statusSelect = document.getElementById('statusSelect').value;
            const typeSelect = document.getElementById('typeSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const merchantSearch = document.getElementById('merchantSearch').value;

            // 这里应该调用API进行筛选查询
            console.log('应用筛选条件:', {
                card: cardSelect,
                status: statusSelect,
                type: typeSelect,
                startDate,
                endDate,
                merchant: merchantSearch
            });

            // 模拟筛选结果
            alert('筛选条件已应用，正在查询...');
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('cardSelect').value = '';
            document.getElementById('statusSelect').value = '';
            document.getElementById('typeSelect').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            document.getElementById('merchantSearch').value = '';
        }

        // 分页功能
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                updatePagination();
                // 这里应该加载新页面的数据
                console.log('切换到第', currentPage, '页');
            }
        }

        function updatePagination() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('transactionDetailModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePagination();
            
            // 设置默认日期范围（最近30天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
