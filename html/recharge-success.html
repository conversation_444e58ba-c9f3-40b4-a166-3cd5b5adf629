<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值成功</title>
    <style>
        .page-content {
            padding: 32px;
            background: #161a1e;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .success-container {
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 32px;
            font-size: 40px;
            color: white;
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .success-title {
            font-size: 32px;
            font-weight: 700;
            color: #f0f6fc;
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        .success-subtitle {
            font-size: 18px;
            color: #8b949e;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .recharge-details {
            background: #21262d;
            border: 1px solid #2a2e33;
            border-radius: 12px;
            padding: 32px;
            margin: 40px 0;
            text-align: left;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #2a2e33;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-size: 16px;
            color: #8b949e;
            font-weight: 500;
        }

        .detail-value {
            font-size: 16px;
            color: #f0f6fc;
            font-weight: 600;
        }

        .detail-value.highlight {
            color: #f7931a;
            font-size: 20px;
        }

        .card-preview {
            background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
            border-radius: 16px;
            padding: 24px;
            margin: 32px 0;
            color: white;
            text-align: left;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .card-label {
            font-size: 18px;
            font-weight: 600;
        }

        .card-number {
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 2px;
            margin-bottom: 16px;
            font-family: 'Courier New', monospace;
        }

        .card-balance {
            text-align: right;
        }

        .balance-label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .balance-amount {
            font-size: 28px;
            font-weight: 700;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #f7931a;
            color: white;
        }

        .btn-primary:hover {
            background: #e8851e;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #2a2e33;
        }

        .btn-secondary:hover {
            background: #2a2e33;
            transform: translateY(-2px);
        }

        .transaction-info {
            background: #1a1f24;
            border: 1px solid #2a2e33;
            border-radius: 8px;
            padding: 20px;
            margin-top: 32px;
            text-align: left;
        }

        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #f7931a;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .info-item-label {
            font-size: 14px;
            color: #8b949e;
        }

        .info-item-value {
            font-size: 14px;
            color: #f0f6fc;
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .card-balance {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <div class="success-container">
            <div class="success-icon">✓</div>
            
            <h1 class="success-title">充值成功！</h1>
            <p class="success-subtitle">
                您的卡片充值已成功完成，资金已到账。<br>
                您现在可以使用这张卡片进行消费了。
            </p>

            <!-- 充值详情 -->
            <div class="recharge-details">
                <div class="detail-row">
                    <span class="detail-label">充值金额</span>
                    <span class="detail-value highlight" id="rechargeAmount">$500.00</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">手续费</span>
                    <span class="detail-value" id="rechargeFee">$10.00</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">扣款账户</span>
                    <span class="detail-value" id="sourceAccount">USDT 账户</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">扣款金额</span>
                    <span class="detail-value" id="totalDeducted">510.02 USDT</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">充值时间</span>
                    <span class="detail-value" id="rechargeTime">2024-01-20 16:30:25</span>
                </div>
            </div>

            <!-- 卡片预览 -->
            <div class="card-preview">
                <div class="card-header">
                    <div class="card-label" id="cardLabel">购物卡</div>
                </div>
                <div class="card-number" id="cardNumber">**** **** **** 1234</div>
                <div class="card-balance">
                    <div class="balance-label">当前余额</div>
                    <div class="balance-amount" id="newBalance">$1,750.00</div>
                </div>
            </div>

            <!-- 交易信息 -->
            <div class="transaction-info">
                <div class="info-title">
                    📋 交易信息
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-item-label">交易ID</span>
                        <span class="info-item-value" id="transactionId">TXN20240120163025001</span>
                    </div>
                    <div class="info-item">
                        <span class="info-item-label">区块链哈希</span>
                        <span class="info-item-value" id="blockchainHash">0x1a2b3c4d...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-item-label">汇率</span>
                        <span class="info-item-value" id="exchangeRate">1 USDT = 0.9998 USD</span>
                    </div>
                    <div class="info-item">
                        <span class="info-item-label">状态</span>
                        <span class="info-item-value" style="color: #28a745;">已确认</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="#" class="btn btn-primary" onclick="goToCardManagement()">
                    💳 查看卡片详情
                </a>
                <a href="#" class="btn btn-secondary" onclick="continueRecharge()">
                    💰 继续充值
                </a>
            </div>
        </div>
    </div>

    <script>
        // 从localStorage获取充值信息
        function loadRechargeData() {
            const rechargeData = JSON.parse(localStorage.getItem('rechargeData') || '{}');
            
            if (rechargeData.amount) {
                document.getElementById('rechargeAmount').textContent = `$${rechargeData.amount}`;
            }
            if (rechargeData.fee) {
                document.getElementById('rechargeFee').textContent = `$${rechargeData.fee}`;
            }
            if (rechargeData.sourceAccount) {
                document.getElementById('sourceAccount').textContent = rechargeData.sourceAccount;
            }
            if (rechargeData.totalDeducted) {
                document.getElementById('totalDeducted').textContent = rechargeData.totalDeducted;
            }
            if (rechargeData.cardLabel) {
                document.getElementById('cardLabel').textContent = rechargeData.cardLabel;
            }
            if (rechargeData.cardNumber) {
                document.getElementById('cardNumber').textContent = rechargeData.cardNumber;
            }
            if (rechargeData.newBalance) {
                document.getElementById('newBalance').textContent = `$${rechargeData.newBalance}`;
            }
            
            // 设置当前时间
            document.getElementById('rechargeTime').textContent = new Date().toLocaleString('zh-CN');
            
            // 生成随机交易ID和哈希
            const transactionId = 'TXN' + new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14) + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            document.getElementById('transactionId').textContent = transactionId;
            
            const hash = '0x' + Math.random().toString(16).substr(2, 8) + '...';
            document.getElementById('blockchainHash').textContent = hash;
        }

        // 跳转到卡片管理
        function goToCardManagement() {
            if (window.parent && window.parent.loadPage) {
                window.parent.loadPage('card-management');
            }
        }

        // 继续充值
        function continueRecharge() {
            if (window.parent && window.parent.loadPage) {
                window.parent.loadPage('card-recharge');
            }
        }

        // 页面加载时获取充值数据
        document.addEventListener('DOMContentLoaded', loadRechargeData);
    </script>
</body>
</html>
