<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>余额查询</title>
    <style>
        .page-content {
            padding: 32px;
            background: #161a1e;
            min-height: 100vh;
        }

        .balance-overview {
            background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
            color: white;
            padding: 48px;
            border-radius: 16px;
            margin-bottom: 32px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .overview-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .overview-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .overview-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .total-balance {
            text-align: center;
            margin-bottom: 30px;
        }

        .balance-amount {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .balance-label {
            font-size: 16px;
            opacity: 0.9;
        }

        .balance-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .balance-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card-name {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .card-number {
            font-size: 12px;
            opacity: 0.7;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
        }

        .card-balance {
            font-size: 24px;
            font-weight: 600;
        }

        .details-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px 25px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-controls select,
        .filter-controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .filter-controls select:focus,
        .filter-controls input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn-filter {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-filter:hover {
            background: #5a6fd8;
        }

        .flow-list {
            padding: 0;
        }

        .flow-item {
            padding: 20px 25px;
            border-bottom: 1px solid #f1f3f4;
            transition: background-color 0.3s ease;
        }

        .flow-item:hover {
            background-color: #f8f9fa;
        }

        .flow-item:last-child {
            border-bottom: none;
        }

        .flow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .flow-type {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .flow-amount {
            font-size: 16px;
            font-weight: 600;
        }

        .amount-in {
            color: #27ae60;
        }

        .amount-out {
            color: #e74c3c;
        }

        .flow-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #7f8c8d;
        }

        .flow-time {
            margin-right: 15px;
        }

        .flow-card {
            font-size: 12px;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            color: #495057;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .summary-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }

        .summary-value {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 14px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .empty-state p {
            font-size: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        @media (max-width: 768px) {
            .balance-overview {
                padding: 25px;
            }
            
            .balance-amount {
                font-size: 36px;
            }
            
            .balance-cards {
                grid-template-columns: 1fr;
            }
            
            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filter-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }
            
            .flow-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .flow-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <!-- 余额总览 -->
        <div class="balance-overview">
            <div class="overview-header">
                <h2 class="overview-title">账户总览</h2>
                <p class="overview-subtitle">实时更新您的账户余额信息</p>
            </div>
            
            <div class="total-balance">
                <div class="balance-amount">$2,750.00</div>
                <div class="balance-label">总余额 (所有卡片)</div>
            </div>
            
            <div class="balance-cards">
                <div class="balance-card">
                    <div class="card-name">购物卡</div>
                    <div class="card-number">**** **** **** 1234</div>
                    <div class="card-balance">$1,250.00</div>
                </div>
                
                <div class="balance-card">
                    <div class="card-name">订阅卡</div>
                    <div class="card-number">**** **** **** 5678</div>
                    <div class="card-balance">$500.00</div>
                </div>
                
                <div class="balance-card">
                    <div class="card-name">备用卡</div>
                    <div class="card-number">**** **** **** 9012</div>
                    <div class="card-balance">$1,000.00</div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-icon">📈</div>
                <div class="summary-value">$1,500.00</div>
                <div class="summary-label">本月充值</div>
            </div>
            
            <div class="summary-card">
                <div class="summary-icon">💳</div>
                <div class="summary-value">$856.42</div>
                <div class="summary-label">本月消费</div>
            </div>
            
            <div class="summary-card">
                <div class="summary-icon">💰</div>
                <div class="summary-value">$25.50</div>
                <div class="summary-label">本月手续费</div>
            </div>
            
            <div class="summary-card">
                <div class="summary-icon">🔄</div>
                <div class="summary-value">$89.99</div>
                <div class="summary-label">本月退款</div>
            </div>
        </div>

        <!-- 资金流水 -->
        <div class="details-section">
            <div class="section-header">
                <h3 class="section-title">
                    <span class="icon">💸</span>
                    资金流水
                </h3>
                <div class="filter-controls">
                    <select id="cardFilter">
                        <option value="">全部卡片</option>
                        <option value="1234">购物卡</option>
                        <option value="5678">订阅卡</option>
                        <option value="9012">备用卡</option>
                    </select>
                    <select id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="recharge">充值</option>
                        <option value="consume">消费</option>
                        <option value="refund">退款</option>
                        <option value="fee">手续费</option>
                    </select>
                    <input type="date" id="dateFilter">
                    <button class="btn-filter" onclick="applyFilter()">筛选</button>
                </div>
            </div>
            
            <div class="flow-list" id="flowList">
                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">USDT 充值</div>
                        <div class="flow-amount amount-in">+$500.00</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-20 16:22:10</span>
                        </div>
                        <div class="flow-card">购物卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">Amazon 消费</div>
                        <div class="flow-amount amount-out">-$89.99</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-20 14:30:25</span>
                        </div>
                        <div class="flow-card">购物卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">充值手续费</div>
                        <div class="flow-amount amount-out">-$10.00</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-20 16:22:10</span>
                        </div>
                        <div class="flow-card">购物卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">Netflix 订阅</div>
                        <div class="flow-amount amount-out">-$15.99</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-19 09:15:42</span>
                        </div>
                        <div class="flow-card">订阅卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">Spotify 退款</div>
                        <div class="flow-amount amount-in">+$9.99</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-18 11:30:15</span>
                        </div>
                        <div class="flow-card">订阅卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">USDC 充值</div>
                        <div class="flow-amount amount-in">+$1,000.00</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-15 10:45:30</span>
                        </div>
                        <div class="flow-card">备用卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">开卡费用</div>
                        <div class="flow-amount amount-out">-$5.00</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-15 10:45:30</span>
                        </div>
                        <div class="flow-card">备用卡</div>
                    </div>
                </div>

                <div class="flow-item">
                    <div class="flow-header">
                        <div class="flow-type">月费扣除</div>
                        <div class="flow-amount amount-out">-$2.00</div>
                    </div>
                    <div class="flow-details">
                        <div>
                            <span class="flow-time">2024-01-01 00:00:00</span>
                        </div>
                        <div class="flow-card">购物卡</div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="icon">💸</div>
                <h3>暂无资金流水</h3>
                <p>没有找到符合条件的资金流水记录</p>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <button onclick="changePage(-1)" id="prevBtn">上一页</button>
                <button class="current">1</button>
                <button onclick="changePage(1)">2</button>
                <button onclick="changePage(1)">3</button>
                <button onclick="changePage(1)" id="nextBtn">下一页</button>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 3;

        // 应用筛选条件
        function applyFilter() {
            const cardFilter = document.getElementById('cardFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;

            // 这里应该调用API进行筛选查询
            console.log('应用筛选条件:', {
                card: cardFilter,
                type: typeFilter,
                date: dateFilter
            });

            // 模拟筛选结果
            const flowList = document.getElementById('flowList');
            const emptyState = document.getElementById('emptyState');

            if (typeFilter === 'fee') {
                // 模拟只显示手续费记录
                flowList.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                flowList.style.display = 'block';
                emptyState.style.display = 'none';
            }

            alert('筛选条件已应用');
        }

        // 分页功能
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                updatePagination();
                // 这里应该加载新页面的数据
                console.log('切换到第', currentPage, '页');
            }
        }

        function updatePagination() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;
        }

        // 实时更新余额（模拟）
        function updateBalance() {
            // 这里可以定期调用API更新余额
            console.log('更新余额信息');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePagination();
            
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('dateFilter').value = today;

            // 每30秒更新一次余额（模拟）
            setInterval(updateBalance, 30000);
        });

        // 格式化金额显示
        function formatAmount(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        }

        // 导出功能（可选）
        function exportData() {
            // 导出资金流水数据
            alert('导出功能开发中...');
        }
    </script>
</body>
</html>
