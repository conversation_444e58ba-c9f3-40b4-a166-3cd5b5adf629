<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知中心</title>
    <style>
        .page-content {
            padding: 0;
        }

        .notification-header {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-right: 20px;
        }

        .notification-count {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .filter-section {
            background: white;
            padding: 20px 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            white-space: nowrap;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            min-width: 120px;
        }

        .filter-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .notification-list {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .notification-item {
            padding: 20px 25px;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: #f8f9ff;
            border-left: 4px solid #667eea;
        }

        .notification-item.unread::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 25px;
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border-radius: 50%;
        }

        .notification-header-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .notification-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .notification-time {
            font-size: 12px;
            color: #7f8c8d;
            white-space: nowrap;
        }

        .notification-content {
            font-size: 14px;
            color: #555;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .notification-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-type {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-success {
            background: #d4edda;
            color: #155724;
        }

        .type-warning {
            background: #fff3cd;
            color: #856404;
        }

        .type-error {
            background: #f8d7da;
            color: #721c24;
        }

        .type-info {
            background: #cce5ff;
            color: #004085;
        }

        .type-system {
            background: #e2e3e5;
            color: #383d41;
        }

        .notification-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 4px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-mark-read {
            background: #28a745;
            color: white;
        }

        .btn-mark-read:hover {
            background: #218838;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .empty-state p {
            font-size: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* 设置模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .settings-group {
            margin-bottom: 25px;
        }

        .settings-group h4 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-size: 14px;
            color: #2c3e50;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle.active {
            background: #667eea;
        }

        .toggle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle.active::before {
            transform: translateX(26px);
        }

        @media (max-width: 768px) {
            .notification-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .header-left {
                justify-content: center;
            }
            
            .header-actions {
                justify-content: center;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                flex-direction: column;
                align-items: stretch;
                gap: 5px;
            }
            
            .notification-header-item {
                flex-direction: column;
                gap: 5px;
            }
            
            .notification-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <!-- 通知中心头部 -->
        <div class="notification-header">
            <div class="header-left">
                <h2 class="header-title">通知中心</h2>
                <span class="notification-count" id="unreadCount">5</span>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="markAllAsRead()">全部已读</button>
                <button class="btn btn-primary" onclick="openSettings()">通知设置</button>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="typeFilter">通知类型:</label>
                    <select id="typeFilter" onchange="applyFilter()">
                        <option value="">全部类型</option>
                        <option value="success">成功通知</option>
                        <option value="warning">警告通知</option>
                        <option value="error">错误通知</option>
                        <option value="info">信息通知</option>
                        <option value="system">系统通知</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="statusFilter">阅读状态:</label>
                    <select id="statusFilter" onchange="applyFilter()">
                        <option value="">全部状态</option>
                        <option value="unread">未读</option>
                        <option value="read">已读</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="timeFilter">时间范围:</label>
                    <select id="timeFilter" onchange="applyFilter()">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">最近一周</option>
                        <option value="month">最近一月</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 通知列表 -->
        <div class="notification-list">
            <div class="notification-item unread" onclick="markAsRead(this, 'n001')">
                <div class="notification-header-item">
                    <div>
                        <div class="notification-title">充值成功通知</div>
                        <div class="notification-time">2024-01-20 16:25:30</div>
                    </div>
                </div>
                <div class="notification-content">
                    您的购物卡充值 $500.00 已成功到账，当前余额 $1,250.00。交易ID: TXN20240120162530001
                </div>
                <div class="notification-meta">
                    <div class="notification-type type-success">充值成功</div>
                    <div class="notification-actions">
                        <button class="btn-small btn-mark-read" onclick="event.stopPropagation(); markAsRead(this.closest('.notification-item'), 'n001')">标记已读</button>
                        <button class="btn-small btn-delete" onclick="event.stopPropagation(); deleteNotification('n001')">删除</button>
                    </div>
                </div>
            </div>

            <div class="notification-item unread" onclick="markAsRead(this, 'n002')">
                <div class="notification-header-item">
                    <div>
                        <div class="notification-title">交易失败通知</div>
                        <div class="notification-time">2024-01-20 14:35:15</div>
                    </div>
                </div>
                <div class="notification-content">
                    您在 Steam Store 的交易 $59.99 失败，原因：余额不足。请及时充值后重试。
                </div>
                <div class="notification-meta">
                    <div class="notification-type type-error">交易失败</div>
                    <div class="notification-actions">
                        <button class="btn-small btn-mark-read" onclick="event.stopPropagation(); markAsRead(this.closest('.notification-item'), 'n002')">标记已读</button>
                        <button class="btn-small btn-delete" onclick="event.stopPropagation(); deleteNotification('n002')">删除</button>
                    </div>
                </div>
            </div>

            <div class="notification-item unread" onclick="markAsRead(this, 'n003')">
                <div class="notification-header-item">
                    <div>
                        <div class="notification-title">卡片状态变更</div>
                        <div class="notification-time">2024-01-19 10:20:45</div>
                    </div>
                </div>
                <div class="notification-content">
                    您的订阅卡 (**** 5678) 已被冻结。如有疑问，请联系客服。
                </div>
                <div class="notification-meta">
                    <div class="notification-type type-warning">状态变更</div>
                    <div class="notification-actions">
                        <button class="btn-small btn-mark-read" onclick="event.stopPropagation(); markAsRead(this.closest('.notification-item'), 'n003')">标记已读</button>
                        <button class="btn-small btn-delete" onclick="event.stopPropagation(); deleteNotification('n003')">删除</button>
                    </div>
                </div>
            </div>

            <div class="notification-item" onclick="markAsRead(this, 'n004')">
                <div class="notification-header-item">
                    <div>
                        <div class="notification-title">交易成功通知</div>
                        <div class="notification-time">2024-01-19 09:15:42</div>
                    </div>
                </div>
                <div class="notification-content">
                    您在 Netflix 的订阅费用 $15.99 支付成功。服务已激活，感谢您的使用。
                </div>
                <div class="notification-meta">
                    <div class="notification-type type-success">交易成功</div>
                    <div class="notification-actions">
                        <button class="btn-small btn-delete" onclick="event.stopPropagation(); deleteNotification('n004')">删除</button>
                    </div>
                </div>
            </div>

            <div class="notification-item unread" onclick="markAsRead(this, 'n005')">
                <div class="notification-header-item">
                    <div>
                        <div class="notification-title">系统维护通知</div>
                        <div class="notification-time">2024-01-18 20:00:00</div>
                    </div>
                </div>
                <div class="notification-content">
                    系统将于 2024-01-21 02:00-04:00 进行维护升级，期间可能影响部分功能使用，请提前做好安排。
                </div>
                <div class="notification-meta">
                    <div class="notification-type type-system">系统维护</div>
                    <div class="notification-actions">
                        <button class="btn-small btn-mark-read" onclick="event.stopPropagation(); markAsRead(this.closest('.notification-item'), 'n005')">标记已读</button>
                        <button class="btn-small btn-delete" onclick="event.stopPropagation(); deleteNotification('n005')">删除</button>
                    </div>
                </div>
            </div>

            <div class="notification-item" onclick="markAsRead(this, 'n006')">
                <div class="notification-header-item">
                    <div>
                        <div class="notification-title">退款到账通知</div>
                        <div class="notification-time">2024-01-16 11:30:15</div>
                    </div>
                </div>
                <div class="notification-content">
                    您的 Spotify 退款 $9.99 已成功到账至订阅卡，当前余额 $509.99。
                </div>
                <div class="notification-meta">
                    <div class="notification-type type-info">退款到账</div>
                    <div class="notification-actions">
                        <button class="btn-small btn-delete" onclick="event.stopPropagation(); deleteNotification('n006')">删除</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="icon">🔔</div>
            <h3>暂无通知</h3>
            <p>没有找到符合条件的通知</p>
        </div>

        <!-- 分页 -->
        <div class="pagination">
            <button onclick="changePage(-1)" id="prevBtn">上一页</button>
            <button class="current">1</button>
            <button onclick="changePage(1)">2</button>
            <button onclick="changePage(1)" id="nextBtn">下一页</button>
        </div>

        <!-- 通知设置模态框 -->
        <div id="settingsModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeSettings()">&times;</span>
                <h3>通知设置</h3>
                
                <div class="settings-group">
                    <h4>通知方式</h4>
                    <div class="setting-item">
                        <span class="setting-label">站内信通知</span>
                        <div class="setting-control">
                            <div class="toggle active" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">邮件通知</span>
                        <div class="setting-control">
                            <div class="toggle active" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">短信通知</span>
                        <div class="setting-control">
                            <div class="toggle" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h4>通知类型</h4>
                    <div class="setting-item">
                        <span class="setting-label">交易通知</span>
                        <div class="setting-control">
                            <div class="toggle active" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">充值通知</span>
                        <div class="setting-control">
                            <div class="toggle active" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">卡片状态通知</span>
                        <div class="setting-control">
                            <div class="toggle active" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">系统通知</span>
                        <div class="setting-control">
                            <div class="toggle active" onclick="toggleSetting(this)"></div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn btn-primary" onclick="saveSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 2;

        // 标记单个通知为已读
        function markAsRead(element, notificationId) {
            element.classList.remove('unread');
            updateUnreadCount();
            
            // 这里应该调用API标记为已读
            console.log('标记通知为已读:', notificationId);
        }

        // 标记全部为已读
        function markAllAsRead() {
            const unreadItems = document.querySelectorAll('.notification-item.unread');
            unreadItems.forEach(item => {
                item.classList.remove('unread');
            });
            updateUnreadCount();
            
            // 这里应该调用API标记全部为已读
            console.log('标记全部通知为已读');
        }

        // 删除通知
        function deleteNotification(notificationId) {
            if (confirm('确认删除此通知？')) {
                // 找到对应的通知元素并删除
                const notificationElement = document.querySelector(`[onclick*="${notificationId}"]`);
                if (notificationElement) {
                    notificationElement.remove();
                    updateUnreadCount();
                }
                
                // 这里应该调用API删除通知
                console.log('删除通知:', notificationId);
            }
        }

        // 更新未读数量
        function updateUnreadCount() {
            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
            document.getElementById('unreadCount').textContent = unreadCount;
            
            if (unreadCount === 0) {
                document.getElementById('unreadCount').style.display = 'none';
            } else {
                document.getElementById('unreadCount').style.display = 'inline-block';
            }
        }

        // 应用筛选条件
        function applyFilter() {
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;

            // 这里应该调用API进行筛选
            console.log('应用筛选条件:', {
                type: typeFilter,
                status: statusFilter,
                time: timeFilter
            });

            // 模拟筛选结果
            const notificationList = document.querySelector('.notification-list');
            const emptyState = document.getElementById('emptyState');

            if (statusFilter === 'read' && typeFilter === 'error') {
                // 模拟没有符合条件的通知
                notificationList.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                notificationList.style.display = 'block';
                emptyState.style.display = 'none';
            }
        }

        // 打开通知设置
        function openSettings() {
            document.getElementById('settingsModal').style.display = 'block';
        }

        // 关闭通知设置
        function closeSettings() {
            document.getElementById('settingsModal').style.display = 'none';
        }

        // 切换设置开关
        function toggleSetting(element) {
            element.classList.toggle('active');
        }

        // 保存设置
        function saveSettings() {
            const settings = {};
            const toggles = document.querySelectorAll('.toggle');
            
            toggles.forEach((toggle, index) => {
                settings[`setting_${index}`] = toggle.classList.contains('active');
            });

            // 这里应该调用API保存设置
            console.log('保存通知设置:', settings);
            
            alert('通知设置已保存');
            closeSettings();
        }

        // 分页功能
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                updatePagination();
                // 这里应该加载新页面的数据
                console.log('切换到第', currentPage, '页');
            }
        }

        function updatePagination() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('settingsModal');
            if (event.target === modal) {
                closeSettings();
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePagination();
            updateUnreadCount();
        });
    </script>
</body>
</html>
