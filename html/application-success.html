<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申请成功</title>
    <style>
        .page-content {
            padding: 32px;
            background: #161a1e;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .success-container {
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 32px;
            font-size: 40px;
            color: white;
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .success-title {
            font-size: 32px;
            font-weight: 700;
            color: #f0f6fc;
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        .success-subtitle {
            font-size: 18px;
            color: #8b949e;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .card-preview {
            background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
            border-radius: 16px;
            padding: 32px;
            margin: 40px 0;
            color: white;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .card-label {
            font-size: 18px;
            font-weight: 600;
        }

        .card-type {
            font-size: 14px;
            opacity: 0.9;
        }

        .card-number {
            font-size: 24px;
            font-weight: 600;
            letter-spacing: 3px;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
        }

        .card-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-detail-item {
            text-align: left;
        }

        .card-detail-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .card-detail-value {
            font-size: 16px;
            font-weight: 600;
        }

        .card-balance {
            text-align: right;
        }

        .balance-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .balance-amount {
            font-size: 28px;
            font-weight: 700;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .info-card {
            background: #21262d;
            border: 1px solid #2a2e33;
            border-radius: 12px;
            padding: 24px;
            text-align: left;
        }

        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #f0f6fc;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            color: #8b949e;
        }

        .info-value {
            color: #f0f6fc;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #f7931a;
            color: white;
        }

        .btn-primary:hover {
            background: #e8851e;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #21262d;
            color: #f0f6fc;
            border: 1px solid #2a2e33;
        }

        .btn-secondary:hover {
            background: #2a2e33;
            transform: translateY(-2px);
        }

        .security-notice {
            background: #1a1f24;
            border: 1px solid #2a2e33;
            border-radius: 8px;
            padding: 20px;
            margin-top: 32px;
            text-align: left;
        }

        .notice-title {
            font-size: 16px;
            font-weight: 600;
            color: #f7931a;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .notice-content {
            font-size: 14px;
            color: #8b949e;
            line-height: 1.6;
        }

        .notice-list {
            margin: 12px 0;
            padding-left: 20px;
        }

        .notice-list li {
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .card-details {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }
            
            .card-balance {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <div class="success-container">
            <div class="success-icon">✓</div>
            
            <h1 class="success-title">开卡申请成功！</h1>
            <p class="success-subtitle">
                恭喜您！您的UCard虚拟信用卡已成功创建并完成初始充值。<br>
                您现在可以开始使用这张卡片进行线上消费了。
            </p>

            <!-- 卡片预览 -->
            <div class="card-preview">
                <div class="card-preview-header">
                    <div class="card-label" id="cardLabel">购物卡</div>
                    <div class="card-type">Virtual Card</div>
                </div>
                <div class="card-number" id="cardNumber">4532 1234 5678 9876</div>
                <div class="card-details">
                    <div class="card-detail-item">
                        <div class="card-detail-label">有效期</div>
                        <div class="card-detail-value" id="expiryDate">12/27</div>
                    </div>
                    <div class="card-detail-item">
                        <div class="card-detail-label">CVV</div>
                        <div class="card-detail-value">***</div>
                    </div>
                    <div class="card-balance">
                        <div class="balance-label">当前余额</div>
                        <div class="balance-amount" id="cardBalance">$500.00</div>
                    </div>
                </div>
            </div>

            <!-- 申请信息 -->
            <div class="info-grid">
                <div class="info-card">
                    <div class="info-title">申请信息</div>
                    <div class="info-item">
                        <span class="info-label">申请时间:</span>
                        <span class="info-value" id="applicationTime">2024-01-20 16:30:25</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用卡人:</span>
                        <span class="info-value" id="cardholderName">John Smith</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">手机号:</span>
                        <span class="info-value" id="phoneNumber">******-567-890</span>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-title">费用明细</div>
                    <div class="info-item">
                        <span class="info-label">充值金额:</span>
                        <span class="info-value" id="rechargeAmount">$500.00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">充值手续费:</span>
                        <span class="info-value" id="rechargeFee">$10.00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">开卡费用:</span>
                        <span class="info-value">$5.00</span>
                    </div>
                    <div class="info-item" style="border-top: 1px solid #2a2e33; padding-top: 8px; margin-top: 8px;">
                        <span class="info-label">总计扣款:</span>
                        <span class="info-value" id="totalDeducted">515.08 USDT</span>
                    </div>
                </div>
            </div>

            <!-- 安全提醒 -->
            <div class="security-notice">
                <div class="notice-title">
                    🔒 安全提醒
                </div>
                <div class="notice-content">
                    为了保障您的资金安全，请注意以下事项：
                    <ul class="notice-list">
                        <li>请妥善保管您的卡片信息，不要向任何人透露完整的卡号、有效期和CVV</li>
                        <li>CVV码仅在需要时通过安全验证后查看</li>
                        <li>建议设置合理的消费限额，避免异常交易</li>
                        <li>如发现异常交易或卡片信息泄露，请立即联系客服冻结卡片</li>
                    </ul>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="#" class="btn btn-primary" onclick="goToCardManagement()">
                    💳 查看卡片详情
                </a>
                <a href="#" class="btn btn-secondary" onclick="goToRecharge()">
                    💰 继续充值
                </a>
            </div>
        </div>
    </div>

    <script>
        // 从URL参数或localStorage获取申请信息
        function loadApplicationData() {
            // 这里可以从实际的申请数据中获取信息
            const applicationData = JSON.parse(localStorage.getItem('applicationData') || '{}');
            
            if (applicationData.cardLabel) {
                document.getElementById('cardLabel').textContent = applicationData.cardLabel;
            }
            if (applicationData.cardholderName) {
                document.getElementById('cardholderName').textContent = applicationData.cardholderName;
            }
            if (applicationData.phoneNumber) {
                document.getElementById('phoneNumber').textContent = applicationData.phoneNumber;
            }
            if (applicationData.rechargeAmount) {
                document.getElementById('rechargeAmount').textContent = `$${applicationData.rechargeAmount}`;
                document.getElementById('cardBalance').textContent = `$${applicationData.rechargeAmount}`;
            }
            if (applicationData.rechargeFee) {
                document.getElementById('rechargeFee').textContent = `$${applicationData.rechargeFee}`;
            }
            if (applicationData.totalDeducted) {
                document.getElementById('totalDeducted').textContent = applicationData.totalDeducted;
            }
            
            // 设置当前时间
            document.getElementById('applicationTime').textContent = new Date().toLocaleString('zh-CN');
        }

        // 跳转到卡片管理
        function goToCardManagement() {
            if (window.parent && window.parent.loadPage) {
                window.parent.loadPage('card-management');
            }
        }

        // 跳转到充值页面
        function goToRecharge() {
            if (window.parent && window.parent.loadPage) {
                window.parent.loadPage('card-recharge');
            }
        }

        // 页面加载时获取申请数据
        document.addEventListener('DOMContentLoaded', loadApplicationData);
    </script>
</body>
</html>
