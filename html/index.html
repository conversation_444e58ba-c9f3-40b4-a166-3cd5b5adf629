<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UCard 用户中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0c0e12;
            color: #eaecef;
            margin: 0;
            padding: 0;
        }

        .container {
            display: flex;
            min-height: 100vh;
            background: #0c0e12;
        }

        /* 左侧菜单 */
        .sidebar {
            width: 280px;
            background: #161a1e;
            border-right: 1px solid #2a2e33;
            padding: 0;
            position: relative;
        }

        .logo {
            padding: 32px 24px;
            border-bottom: 1px solid #2a2e33;
            margin-bottom: 8px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            color: #f7931a;
            margin: 0;
            letter-spacing: -0.5px;
        }

        .logo p {
            font-size: 13px;
            color: #8b949e;
            margin: 4px 0 0 0;
            font-weight: 400;
        }

        .menu {
            list-style: none;
            padding: 8px 0;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-item a {
            display: flex;
            align-items: center;
            padding: 14px 24px;
            color: #8b949e;
            text-decoration: none;
            transition: all 0.2s ease;
            border-radius: 0;
            font-weight: 500;
            font-size: 14px;
        }

        .menu-item a:hover {
            background: #21262d;
            color: #f0f6fc;
        }

        .menu-item a.active {
            background: #21262d;
            color: #f7931a;
            border-right: 2px solid #f7931a;
        }

        .menu-item .icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #0c0e12;
        }

        .content-header {
            background: #161a1e;
            padding: 32px;
            border-radius: 12px;
            border: 1px solid #2a2e33;
            margin-bottom: 24px;
        }

        .content-header h2 {
            font-size: 32px;
            color: #f0f6fc;
            margin-bottom: 8px;
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .content-header p {
            color: #8b949e;
            font-size: 16px;
            margin: 0;
            font-weight: 400;
        }

        .content-body {
            background: #161a1e;
            padding: 0;
            border-radius: 12px;
            border: 1px solid #2a2e33;
            min-height: 500px;
            overflow: hidden;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .main-content {
                order: 1;
                padding: 20px;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧菜单 -->
        <nav class="sidebar">
            <div class="logo">
                <h1>UCard</h1>
                <p>数字货币虚拟信用卡</p>
            </div>
            
            <ul class="menu">
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-management')" class="active">
                        <span class="icon">💳</span>
                        <span>卡片管理</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-application')">
                        <span class="icon">📝</span>
                        <span>开卡申请</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-recharge')">
                        <span class="icon">💰</span>
                        <span>卡片充值</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('transaction-query')">
                        <span class="icon">📊</span>
                        <span>交易查询</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('balance-query')">
                        <span class="icon">💵</span>
                        <span>余额查询</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('notification-center')">
                        <span class="icon">🔔</span>
                        <span>通知中心</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="main-content">
            <div class="content-header">
                <h2 id="page-title">卡片管理</h2>
                <p id="page-description">管理您的所有UCard虚拟信用卡</p>
            </div>
            
            <div class="content-body" id="content-body">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 页面配置
        const pageConfig = {
            'card-management': {
                title: '卡片管理',
                description: '管理您的所有UCard虚拟信用卡'
            },
            'card-application': {
                title: '开卡申请',
                description: '申请新的UCard虚拟信用卡'
            },
            'card-recharge': {
                title: '卡片充值',
                description: '为您的UCard充值数字货币'
            },
            'transaction-query': {
                title: '交易查询',
                description: '查看您的交易记录和详情'
            },
            'balance-query': {
                title: '余额查询',
                description: '查看账户余额和资金明细'
            },
            'notification-center': {
                title: '通知中心',
                description: '查看系统通知和消息'
            },
            'application-success': {
                title: '申请成功',
                description: '您的开卡申请已成功提交'
            },
            'recharge-success': {
                title: '充值成功',
                description: '您的卡片充值已成功完成'
            }
        };

        // 加载页面内容
        async function loadPage(pageName) {
            // 处理初始加载时没有event对象的情况
            if (typeof event !== 'undefined' && event) {
                // 更新菜单状态
                document.querySelectorAll('.menu-item a').forEach(link => {
                    link.classList.remove('active');
                });
                
                // 确保event.target是有效的并且能找到最近的a元素
                if (event.target && event.target.closest('a')) {
                    event.target.closest('a').classList.add('active');
                } else {
                    // 如果找不到event.target，根据pageName查找并激活对应菜单项
                    const menuLink = document.querySelector(`.menu-item a[onclick*="loadPage('${pageName}')"]`);
                    if (menuLink) menuLink.classList.add('active');
                }
            } else {
                // 初始加载时根据pageName查找并激活对应菜单项
                const menuLink = document.querySelector(`.menu-item a[onclick*="loadPage('${pageName}')"]`);
                if (menuLink) menuLink.classList.add('active');
            }

            // 更新页面标题和描述
            const config = pageConfig[pageName];
            document.getElementById('page-title').textContent = config.title;
            document.getElementById('page-description').textContent = config.description;

            // 显示加载动画
            document.getElementById('content-body').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            `;

            try {
                // 加载页面内容
                const response = await fetch(`${pageName}.html`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                const content = await response.text();

                // 提取body内容
                const parser = new DOMParser();
                const doc = parser.parseFromString(content, 'text/html');
                const bodyContent = doc.querySelector('.page-content');

                if (bodyContent) {
                    document.getElementById('content-body').innerHTML = bodyContent.innerHTML;

                    // 执行页面中的脚本
                    const scripts = Array.from(doc.querySelectorAll('script'));
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.body.appendChild(newScript);
                    });
                } else {
                    throw new Error('页面内容格式错误 - 找不到.page-content元素');
                }
            } catch (error) {
                console.error('页面加载错误:', error);
                document.getElementById('content-body').innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #f7931a;">
                        <h3>页面加载失败</h3>
                        <p>错误信息: ${error.message}</p>
                        <button onclick="loadPage('${pageName}')" style="margin-top: 20px; padding: 10px 20px; background: #f7931a; color: white; border: none; border-radius: 6px; cursor: pointer;">重试</button>
                    </div>
                `;
            }
        }

        // 页面加载完成后默认加载卡片管理页面
        document.addEventListener('DOMContentLoaded', function() {
            loadPage('card-management');
        });
    </script>
</body>
</html>
