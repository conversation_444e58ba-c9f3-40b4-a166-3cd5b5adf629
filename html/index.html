<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UCard 用户中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧菜单 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
        }

        .logo h1 {
            font-size: 24px;
            font-weight: 700;
        }

        .logo p {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }

        .menu {
            list-style: none;
        }

        .menu-item {
            margin: 5px 0;
        }

        .menu-item a {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-item a:hover,
        .menu-item a.active {
            background: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }

        .menu-item .icon {
            margin-right: 12px;
            font-size: 18px;
        }

        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .content-header {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .content-header h2 {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .content-header p {
            color: #7f8c8d;
            font-size: 16px;
        }

        .content-body {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 500px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .main-content {
                order: 1;
                padding: 20px;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧菜单 -->
        <nav class="sidebar">
            <div class="logo">
                <h1>UCard</h1>
                <p>数字货币虚拟信用卡</p>
            </div>
            
            <ul class="menu">
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-management')" class="active">
                        <span class="icon">💳</span>
                        <span>卡片管理</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-application')">
                        <span class="icon">📝</span>
                        <span>开卡申请</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-recharge')">
                        <span class="icon">💰</span>
                        <span>卡片充值</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('transaction-query')">
                        <span class="icon">📊</span>
                        <span>交易查询</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('balance-query')">
                        <span class="icon">💵</span>
                        <span>余额查询</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('notification-center')">
                        <span class="icon">🔔</span>
                        <span>通知中心</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="main-content">
            <div class="content-header">
                <h2 id="page-title">卡片管理</h2>
                <p id="page-description">管理您的所有UCard虚拟信用卡</p>
            </div>
            
            <div class="content-body" id="content-body">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 页面配置
        const pageConfig = {
            'card-management': {
                title: '卡片管理',
                description: '管理您的所有UCard虚拟信用卡'
            },
            'card-application': {
                title: '开卡申请',
                description: '申请新的UCard虚拟信用卡'
            },
            'card-recharge': {
                title: '卡片充值',
                description: '为您的UCard充值数字货币'
            },
            'transaction-query': {
                title: '交易查询',
                description: '查看您的交易记录和详情'
            },
            'balance-query': {
                title: '余额查询',
                description: '查看账户余额和资金明细'
            },
            'notification-center': {
                title: '通知中心',
                description: '查看系统通知和消息'
            }
        };

        // 加载页面内容
        async function loadPage(pageName) {
            // 更新菜单状态
            document.querySelectorAll('.menu-item a').forEach(link => {
                link.classList.remove('active');
            });
            event.target.closest('a').classList.add('active');

            // 更新页面标题和描述
            const config = pageConfig[pageName];
            document.getElementById('page-title').textContent = config.title;
            document.getElementById('page-description').textContent = config.description;

            // 显示加载动画
            document.getElementById('content-body').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            `;

            try {
                // 加载页面内容
                const response = await fetch(`${pageName}.html`);
                const content = await response.text();
                
                // 提取body内容
                const parser = new DOMParser();
                const doc = parser.parseFromString(content, 'text/html');
                const bodyContent = doc.querySelector('.page-content') || doc.body;
                
                document.getElementById('content-body').innerHTML = bodyContent.innerHTML;
            } catch (error) {
                document.getElementById('content-body').innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #e74c3c;">
                        <h3>页面加载失败</h3>
                        <p>请检查网络连接或稍后重试</p>
                    </div>
                `;
            }
        }

        // 页面加载完成后默认加载卡片管理页面
        document.addEventListener('DOMContentLoaded', function() {
            loadPage('card-management');
        });
    </script>
</body>
</html>
