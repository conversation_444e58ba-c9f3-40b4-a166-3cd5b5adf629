<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开卡申请</title>
    <style>
        .page-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .application-form {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .form-group small {
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .quick-select {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-btn:hover,
        .quick-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .calculation-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .calc-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .calc-row.total {
            border-top: 1px solid #ddd;
            padding-top: 10px;
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
        }

        .calc-label {
            color: #6c757d;
        }

        .calc-value {
            font-weight: 500;
        }

        .exchange-rate {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
        }

        .rate-info {
            font-size: 14px;
            color: #1976d2;
            margin-bottom: 5px;
        }

        .rate-time {
            font-size: 12px;
            color: #757575;
        }

        .account-selector {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .account-selector:hover,
        .account-selector.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .account-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .account-type {
            font-weight: 500;
            color: #2c3e50;
        }

        .account-balance {
            font-size: 14px;
            color: #27ae60;
            font-weight: 600;
        }

        .submit-section {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
        }

        .btn-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            min-width: 200px;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
        }

        .btn-submit:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .phone-verification {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .phone-verification input {
            flex: 1;
        }

        .btn-verify {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
        }

        .btn-verify:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }

        .verification-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .phone-verification {
                flex-direction: column;
                align-items: stretch;
            }
            
            .quick-select {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <form class="application-form" id="applicationForm">
            <!-- 开卡信息 -->
            <div class="form-section">
                <h3 class="section-title">
                    <span class="icon">📝</span>
                    开卡信息
                </h3>
                
                <div class="form-group">
                    <label for="cardLabel">卡片标签 <small>(可选)</small></label>
                    <input type="text" id="cardLabel" placeholder="如：购物卡、订阅卡等">
                    <small>为您的卡片设置一个便于识别的标签</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="cardholderName">用卡人姓名</label>
                        <select id="cardholderName">
                            <option value="">请选择或输入用卡人姓名</option>
                            <option value="John Smith">John Smith (KYC信息)</option>
                            <option value="Jane Doe">Jane Doe (常用)</option>
                            <option value="custom">手动输入...</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="customName" id="customNameLabel" style="display: none;">自定义姓名</label>
                        <input type="text" id="customName" placeholder="输入用卡人姓名" style="display: none;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="phoneNumber">用卡人手机号</label>
                    <div class="phone-verification">
                        <select id="phoneSelect">
                            <option value="">选择已验证手机号或输入新号码</option>
                            <option value="+1234567890">******-567-890 (已验证)</option>
                            <option value="+9876543210">+98 765-432-10 (已验证)</option>
                            <option value="new">输入新手机号...</option>
                        </select>
                        <button type="button" class="btn-verify" id="verifyBtn" style="display: none;" onclick="sendVerification()">发送验证码</button>
                    </div>
                    <input type="text" id="newPhone" placeholder="输入新手机号" style="display: none; margin-top: 10px;">
                    <input type="text" id="verificationCode" placeholder="输入验证码" style="display: none; margin-top: 10px;">
                    <div id="verificationStatus" class="verification-status" style="display: none;"></div>
                    <small>用于3DS验证，确保交易安全</small>
                </div>

                <div class="calculation-box">
                    <div class="calc-row">
                        <span class="calc-label">开卡费用:</span>
                        <span class="calc-value">$5.00</span>
                    </div>
                </div>
            </div>

            <!-- 初始充值信息 -->
            <div class="form-section">
                <h3 class="section-title">
                    <span class="icon">💰</span>
                    初始充值信息
                </h3>

                <div class="form-group">
                    <label for="rechargeAmount">充值金额 (USD)</label>
                    <input type="number" id="rechargeAmount" placeholder="输入充值金额" min="10" step="0.01" oninput="calculateTotal()">
                    <div class="quick-select">
                        <button type="button" class="quick-btn" onclick="selectAmount(100)">$100</button>
                        <button type="button" class="quick-btn" onclick="selectAmount(500)">$500</button>
                        <button type="button" class="quick-btn" onclick="selectAmount(1000)">$1000</button>
                    </div>
                    <small>最低充值金额 $10</small>
                </div>

                <div class="form-group">
                    <label>选择扣款账户</label>
                    <div class="account-selector" onclick="selectAccount('usdt')">
                        <div class="account-info">
                            <span class="account-type">USDT 账户</span>
                            <span class="account-balance">余额: 2,500.00 USDT</span>
                        </div>
                    </div>
                    <div class="account-selector" onclick="selectAccount('usdc')">
                        <div class="account-info">
                            <span class="account-type">USDC 账户</span>
                            <span class="account-balance">余额: 1,800.50 USDC</span>
                        </div>
                    </div>
                </div>

                <div class="exchange-rate" id="exchangeRate" style="display: none;">
                    <div class="rate-info">当前汇率: 1 USDT = 0.9998 USD</div>
                    <div class="rate-time">更新时间: 2024-01-20 14:30:00</div>
                </div>

                <div class="calculation-box" id="calculationBox">
                    <div class="calc-row">
                        <span class="calc-label">充值金额:</span>
                        <span class="calc-value" id="chargeAmount">$0.00</span>
                    </div>
                    <div class="calc-row">
                        <span class="calc-label">充值手续费 (2%):</span>
                        <span class="calc-value" id="chargeFee">$0.00</span>
                    </div>
                    <div class="calc-row">
                        <span class="calc-label">开卡费用:</span>
                        <span class="calc-value">$5.00</span>
                    </div>
                    <div class="calc-row total">
                        <span class="calc-label">总计扣款:</span>
                        <span class="calc-value" id="totalAmount">0.00 USDT</span>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-section">
                <button type="submit" class="btn-submit" id="submitBtn" disabled>
                    确认申请开卡
                </button>
                <div style="margin-top: 15px; color: #7f8c8d; font-size: 14px;">
                    点击确认后将扣除相应金额并开始处理您的开卡申请
                </div>
            </div>
        </form>
    </div>

    <script>
        let selectedAccount = '';
        let phoneVerified = false;

        // 处理用卡人姓名选择
        document.getElementById('cardholderName').addEventListener('change', function() {
            const customNameGroup = document.getElementById('customName');
            const customNameLabel = document.getElementById('customNameLabel');
            
            if (this.value === 'custom') {
                customNameGroup.style.display = 'block';
                customNameLabel.style.display = 'block';
            } else {
                customNameGroup.style.display = 'none';
                customNameLabel.style.display = 'none';
            }
            checkFormValidity();
        });

        // 处理手机号选择
        document.getElementById('phoneSelect').addEventListener('change', function() {
            const newPhoneInput = document.getElementById('newPhone');
            const verifyBtn = document.getElementById('verifyBtn');
            const verificationCode = document.getElementById('verificationCode');
            const verificationStatus = document.getElementById('verificationStatus');
            
            if (this.value === 'new') {
                newPhoneInput.style.display = 'block';
                verifyBtn.style.display = 'block';
                phoneVerified = false;
            } else if (this.value) {
                newPhoneInput.style.display = 'none';
                verifyBtn.style.display = 'none';
                verificationCode.style.display = 'none';
                verificationStatus.style.display = 'none';
                phoneVerified = true;
            } else {
                newPhoneInput.style.display = 'none';
                verifyBtn.style.display = 'none';
                verificationCode.style.display = 'none';
                verificationStatus.style.display = 'none';
                phoneVerified = false;
            }
            checkFormValidity();
        });

        // 发送验证码
        function sendVerification() {
            const newPhone = document.getElementById('newPhone').value;
            if (!newPhone) {
                alert('请输入手机号');
                return;
            }
            
            document.getElementById('verificationCode').style.display = 'block';
            document.getElementById('verificationStatus').style.display = 'block';
            document.getElementById('verificationStatus').className = 'verification-status status-success';
            document.getElementById('verificationStatus').textContent = '验证码已发送，请查收短信';
            
            // 模拟验证码验证
            document.getElementById('verificationCode').addEventListener('input', function() {
                if (this.value.length === 6) {
                    phoneVerified = true;
                    document.getElementById('verificationStatus').textContent = '手机号验证成功';
                    checkFormValidity();
                }
            });
        }

        // 选择充值金额
        function selectAmount(amount) {
            document.getElementById('rechargeAmount').value = amount;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            calculateTotal();
        }

        // 选择账户
        function selectAccount(type) {
            selectedAccount = type;
            document.querySelectorAll('.account-selector').forEach(selector => {
                selector.classList.remove('selected');
            });
            event.target.closest('.account-selector').classList.add('selected');
            
            document.getElementById('exchangeRate').style.display = 'block';
            calculateTotal();
            checkFormValidity();
        }

        // 计算总金额
        function calculateTotal() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const chargeFee = rechargeAmount * 0.02; // 2% 手续费
            const openCardFee = 5.00;
            const totalUSD = rechargeAmount + chargeFee + openCardFee;
            
            // 模拟汇率转换
            const exchangeRate = selectedAccount === 'usdt' ? 1.0002 : 0.9998;
            const totalCrypto = totalUSD * exchangeRate;
            
            document.getElementById('chargeAmount').textContent = `$${rechargeAmount.toFixed(2)}`;
            document.getElementById('chargeFee').textContent = `$${chargeFee.toFixed(2)}`;
            document.getElementById('totalAmount').textContent = `${totalCrypto.toFixed(4)} ${selectedAccount.toUpperCase()}`;
            
            checkFormValidity();
        }

        // 检查表单有效性
        function checkFormValidity() {
            const cardholderName = document.getElementById('cardholderName').value;
            const customName = document.getElementById('customName').value;
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            
            const nameValid = cardholderName && (cardholderName !== 'custom' || customName);
            const amountValid = rechargeAmount >= 10;
            const accountValid = selectedAccount !== '';
            
            const isValid = nameValid && phoneVerified && amountValid && accountValid;
            document.getElementById('submitBtn').disabled = !isValid;
        }

        // 表单提交
        document.getElementById('applicationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (confirm('确认提交开卡申请？将扣除相应金额。')) {
                // 模拟提交过程
                document.getElementById('submitBtn').textContent = '处理中...';
                document.getElementById('submitBtn').disabled = true;
                
                setTimeout(() => {
                    alert('开卡申请提交成功！\n\n卡片信息：\n卡号：**** **** **** 9876\n有效期：12/27\n\n请妥善保管您的卡片信息。');
                    // 跳转到卡片管理页面
                    if (window.parent && window.parent.loadPage) {
                        window.parent.loadPage('card-management');
                    }
                }, 2000);
            }
        });

        // 监听充值金额输入
        document.getElementById('rechargeAmount').addEventListener('input', calculateTotal);
    </script>
</body>
</html>
