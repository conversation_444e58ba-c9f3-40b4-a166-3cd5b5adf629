# 产品需求文档 (PRD)：支持 数币充值的UCard平台

## 1. 引言

### 1.1 项目背景与目标

- **背景:** 随着数字货币的普及，越来越多的用户持有 USDT 和 USDC 等稳定币。然而，在传统的线上消费和跨境支付场景中，直接使用数字货币仍然存在诸多限制。高额的跨境支付手续费、繁琐的流程以及部分商户不支持数字货币支付等问题，阻碍了数字货币的广泛应用。本项目旨在构建一个 UCard 系统，通过将 USDT/USDC 转换为法币并绑定到虚拟信用卡上，为用户提供一种便捷、低成本、高效的线上消费和跨境支付解决方案。
- **目标:**
  - 提供一种安全、便捷的方式，允许用户使用 USDT 和 USDC 快速充值到其 UCard 中，并在全球范围内接受 Visa 或 Mastercard 的线上商户进行消费。
  - 显著降低跨境支付的成本和时间。
  - 提升 USDT 和 USDC 的可用性和流通性，促进数字货币生态的发展。
  - 构建一个稳定、可靠、合规的 UCard 运营平台。

### 1.2 术语解释

- **USDT:** Tether，一种与美元挂钩的稳定币。
- **USDC:** USD Coin，一种与美元挂钩的稳定币。
- **UCard:** 支持数币充值的虚拟信用卡，允许用户使用数字货币（USDT/USDC）充值到VCC，然后进行线上消费。
- **VCC:** Virtual Credit Card，虚拟信用卡，没有实体卡片，仅包含卡号、有效期、CVV 等信息，用于线上支付。
- **KYC/AML:** Know Your Customer / Anti-Money Laundering，了解你的客户 / 反洗钱，金融机构和平台必须遵守的合规要求，用于验证用户身份和监控可疑交易。
- **充值地址:** 用户在区块链网络上接收数字货币的唯一地址。
- **Gas Fee:** 在区块链网络上进行交易所需的费用。
- **交易哈希:** 区块链上每笔交易的唯一标识符。
- **发卡机构:** 提供虚拟信用卡发行和清算服务的金融机构。
- **法币:** 法定货币，如美元 (USD)、欧元 (EUR) 等。
- **内部记账单位:** 系统内部用于记录用户余额和交易的单位，可以是法币或其他内部单位。

## 2. 用户故事/用例

### 2.1 用户画像

- **数字货币持有者:** 拥有一定数量的 USDT 或 USDC，希望将数字资产用于日常消费或线上支付。
- **跨境电商卖家/买家:** 需要频繁进行跨境支付，希望寻找一种更便捷、低成本的支付方式。
- **数字游民:** 在不同国家工作和生活，需要一种灵活的支付解决方案。
- **对隐私和安全性有较高要求的用户:** 希望避免使用传统银行卡进行部分线上交易。

### 2.2 核心用户故事

- 故事1: USDT 充值到 UCard 并进行线上购物
  - **作为:** 一名数字货币持有者
  - **我希望:** 能够方便地将我的 USDT 充值到我的 UCard 中
  - **以便:** 在不支持数字货币的电商网站上购买我需要的商品。
- 故事2: USDC 充值 UCard 用于广告费用支付
  - **作为:** 一名跨境电商卖家
  - **我希望:** 能够快速将我的 USDC 充值到 UCard 中
  - **以便:** 及时支付社交媒体平台的广告费用，避免广告投放中断。
- 故事3: 查看 UCard 余额和交易记录
  - **作为:**  一名 UCard 用户
  - **我希望:** 能够随时随地查看我的 UCard 当前余额和详细的消费交易记录
  - **以便:** 了解我的消费情况和管理我的资金。
- 故事4: 申请新的 UCard 并充值
  - **作为:**  一名新用户
  - **我希望:** 能够快速完成身份验证并申请一张新的 UCard，并立即充值一定金额
  - **以便:** 立即开始使用 UCard 进行线上支付。
- 故事5: 冻结/解冻 UCard
  - **作为:**  一名 UCard 用户
  - **我希望:** 在怀疑卡片信息泄露或不再使用时，能够方便地冻结或解冻我的 UCard
  - **以便:** 保障我的资金安全。

## 3. 功能需求

### 3.1 用户注册与登录(略)

### 3.2 KYC/AML 身份验证(略)

### 3.3 数字货币充值 (USDT/USDC)(略)

### 3.4 卡片管理

- **3.4.1 卡片列表:** 显示用户所有卡片列表，每张卡片显示卡片标签、卡号 (部分隐藏)、有效期、当前余额、卡片状态 (正常、冻结、已注销)。
- **3.4.2 查看卡片详情:** 点击卡片列表中的卡片，进入卡片详情页面，显示完整的卡号、有效期、CVV (需要用户进行二次验证才能查看)、开卡时间、卡片类型、关联的账户余额等信息。
- **3.4.3 卡片冻结/解冻:**
  - 用户可以点击按钮冻结或解冻卡片。
  - 冻结后，该卡片将无法进行消费。
  - 解冻后，卡片恢复正常使用。
  - 需要用户确认操作。
- **3.4.4 卡片注销:**
  - 用户可以申请注销不再使用的卡片。
  - 注销前需要确认卡片余额处理方式 (例如，退回账户余额)。
  - 注销后，该卡片将永久失效。
  - 需要用户确认操作。
- **3.4.5 设置消费限额:**
  - 用户可以为单张卡片设置每日或每月的消费限额。
  - 系统在处理交易时检查是否超出限额。

### 3.5 申请开卡 (含充值)

- **3.5.1 开卡信息:**
  - 卡片标签 (可选，用户自定义，如"购物卡", "订阅卡"等)。
  - 用卡人姓名（自动列出KYC信息和常用用卡人信息供选择，可手工添加用卡人姓名）
  - 用卡人手机号，用于3DS验证
    - 新手机号需要短信验证，已验证手机号可以从下拉列表中直接选择使用。
  - 系统自动计算并显示开卡费用 (USD)
- **3.5.2 初始充值信息:**
  - 用户输出或选取需要充值的金额 (USD)，常用金额（$100, $500, $1000）可以从下拉列表中选择。
  - 系统自动计算并显示充值手续费（USD）
  - 用户选择扣款账户： 显示用户账户（USDT/USDC）的余额，供选择
  - 系统自动计算并显示USDT/USDC 到 USD 的兑换率
  - 系统自动计算并显示总计扣款金额（USDT/USDC）
- **3.5.3 余额检查与扣除:** 检查用户账户余额是否足够支付开卡金额，足够则扣除相应金额。
- **3.5.4 调用发卡机构 API:** 调用对接的发卡机构 API，请求生成新的 UCard。
- **3.5.5 风控检查:** 调用风控系统进行开卡申请风控检查（异步）。
- **3.5.6 生成卡片信息:** 接收发卡机构返回的卡片信息，包括卡号、有效期、CVV。
- **3.5.7 显示卡片信息:** 在用户界面上安全地展示卡片信息。出于安全考虑，CVV 可能需要用户进行二次验证才能查看。
- **3.5.8 开卡成功通知:** 通知用户开卡成功，并提供卡片信息概览。

### 3.6 卡片充值

- **3.6.1 选择需要充值的UCard:** 用户从其 UCard 列表中选择要充值的卡片。
- **3.6.2 输入充值信息:**
  - 用户输出或选取需要充值的金额 (USD)，常用金额（$100, $500, $1000）可以从下拉列表中选择。
  - 系统自动计算并显示充值手续费（USD）
  - 用户选择扣款账户： 显示用户账户（USDT/USDC）的余额，供选择
  - 系统自动计算并显示USDT/USDC 到 USD 的兑换率
  - 系统自动计算并显示总计扣款金额（USDT/USDC）
- **3.6.3 余额检查与扣除:** 检查用户账户余额是否足够支付充值金额，足够则扣除相应金额。
- **3.6.4 风控检查:** 调用风控系统进行充值交易风控检查（异步）。
- **3.6.5 调用发卡机构 API:** 调用对接的发卡机构 API，请求向指定的卡片充值。
- **3.6.6 更新卡片余额:** 接收发卡机构返回的充值结果，并在系统中更新卡片的余额。
- **3.6.7 充值成功通知:** 通知用户充值成功。

### 3.7 交易记录查询

- **3.7.1 交易列表:** 显示选定卡片的所有消费交易记录列表。
- **3.7.2 交易详情:** 每条记录包含：交易时间、商户名称、交易金额 (内部记账单位 USD和原始交易货币，如果可能)、交易状态 (成功/失败/退款)、交易类型 (消费、退款)。
  - 失败交易记录
    - 交易失败原因 (例如，余额不足、商户拒绝等)。
  - 成功消费记录
    - 授权成功已入账交易
    - 授权成功但未入账的交易
    - 已退款交易
    - 已取消的交易
  - 消费退款入账记录
  - 争议交易记录

- **3.7.3 筛选与排序:** 支持按时间范围、交易状态、商户名称等条件筛选和排序交易记录。
- **3.7.4 交易详情页面:** 点击单条交易记录，可以查看更详细的信息，例如商户类别、授权码等 (取决于发卡机构提供的接口)。

### 3.8 卡片账户余额管理

- **3.8.1 总余额显示:** 在用户个人中心清晰显示所有卡片的总余额 (USD)。
- **3.8.2 资金明细:** 显示每张卡片的余额和资金的详细变动记录，包括：
  - 充值记录
  - 消费记录：授权成功已入账交易
  - 消费退款入账记录
  - 交易手续费扣除记录（小额交易处理费）
  - 月费扣除记录
- **3.8.3 资金流水查询:** 支持按时间范围、资金类型筛选和查询资金流水。

### 3.9 通知中心

- **3.9.1 通知列表:** 显示所有系统通知列表。
- **3.9.2 通知类型:**
  - 充值成功通知 (包括到账金额)。
  - 交易成功/失败通知 (包括交易金额和商户)。
  - 卡片状态变更通知 (冻结、解冻、注销)。
  - KYC 验证结果通知。
  - 系统维护通知。
- **3.9.3 通知方式:** 支持站内信、邮件、短信等多种通知方式。用户可以设置偏好的通知方式。

### 3.10 帮助与支持

- **3.10.1 常见问题解答 (FAQ):** 提供用户常见问题的解答，例如充值流程、开卡流程、交易失败原因等。
- **3.10.2 联系客服:** 提供多种联系客服的渠道，例如在线聊天、提交工单、客服邮箱。
- **3.10.3 用户指南/教程:** 提供详细的用户指南和操作教程，帮助用户快速上手。

## 4. 非功能性需求

### 4.1 安全性

- 用户敏感数据 (如密码、身份信息) 采用强加密算法存储。
- 卡片信息 (卡号、有效期、CVV) 采用特殊加密方式存储，并在展示时进行脱敏处理，查看 CVV 需要二次验证。
- API 接口采用 OAuth2.0 或其他安全认证和授权机制。
- 部署防火墙和入侵检测系统，防御恶意攻击。
- 定期进行安全漏洞扫描和渗透测试。
- 建立完善的日志审计系统，记录用户操作和系统事件。
- 资金安全保障机制需要与发卡机构的合作协议紧密相关，明确资金托管和清算流程。

### 4.2 性能与可伸缩性

- 系统能够支持50并发用户数的充值和交易请求。
- 核心业务逻辑 (充值监听、兑换、调用发卡机构 API) 需要保证低延迟。
- 采用可伸缩的云基础设施，能够根据用户量增长弹性扩展。
- 数据库读写分离和缓存机制优化性能。

### 4.3 可靠性与稳定性

- 系统架构采用高可用设计，避免单点故障。
- 部署完善的监控系统，实时监测系统各项指标。
- 建立自动报警机制，及时发现和处理异常情况。
- 定期进行数据备份，并建立灾难恢复计划。

### 4.4 合规性

- 严格遵守监管机构的金融法规、反洗钱 (AML) 和反恐怖融资 (CFT) 法规。
- 遵守数据隐私法规。
- 确保与合作的发卡机构具备合规资质。
- 建立内部合规审查流程。

### 4.5 用户体验 (UX)

- 简洁、直观、易于理解的用户界面设计。
- 关键操作流程 (充值、开卡) 简单流畅，减少用户操作步骤。
- 提供清晰的错误提示和操作指引。

## 5. 技术考量与集成

### 5.1 区块链集成（使用现有数币钱包，暂不考虑）

### 5.2 发卡机构集成（使用REAP）

### 5.3 KYC/AML 服务商集成（暂不考虑）

### 5.4 汇率服务

- **选择汇率源:** 选择可靠的实时汇率 API 服务商 (初步使用 CoinMarketCap API)。
- **汇率策略:** 采用实时汇率进行兑换。每30分钟刷新一次。

### 5.6 系统架构

- 推荐采用微服务架构，提高系统的可伸缩性、可维护性和容错性。
- 数据库：PostgreSQL
- 缓存：Redis

### 5.7 安全技术

- SSL/TLS 加密所有通信。
- API Gateway 进行统一的安全认证和流量控制。
- Web 应用防火墙 (WAF)。
- 定期进行代码审计和安全评审。

## 6. 商业模式与盈利模式

### 6.1 盈利模式

- **充值手续费:** 对数字货币充值收取一定比例的手续费。
- **开卡费:** 对申请卡片 收取一次性费用。
- **交易手续费:** 对每笔消费交易收取一定比例或固定金额的手续费 (取决于与发卡机构的协议)。
- **月费/年费:** 对卡片收取月度或年度服务费。
- **汇率差价:** 在数字货币兑换法币时，可能存在一定的买卖价差作为盈利来源。

### 6.2 成本结构

- **技术研发成本:** 开发和维护平台的成本。
- **区块链 Gas Fee 成本:** 处理用户充值交易所需的 Gas Fee。
- **发卡机构费用:** 发卡、交易清算、维护等费用。
- **KYC/AML 服务费用:** 每次验证用户身份的费用。
- **市场推广费用:** 获取新用户的成本。
- **运营和人力成本:** 客户支持、风险管理、合规团队的成本。
